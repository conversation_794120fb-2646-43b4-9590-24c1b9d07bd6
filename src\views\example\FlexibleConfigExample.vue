<template>
  <div class="flexible-config-example">
    <h2>灵活的参数和事件透传示例</h2>
    <p>这个示例展示了如何使用完全灵活的属性和事件透传功能</p>

    <configurable-page
      :config="pageConfig"
      :fetch-data="fetchData"
      :submit-data="submitData"
      @action="handleAction"
      @delete="handleDelete"
      @filter-change="handleFilterChange"
      @dialog-change="handleDialogChange"
    />
  </div>
</template>

<script>
import ConfigurablePage from '@/components/ConfigurablePage'
import { COMPONENT_TYPES, BUTTON_TYPES, TAG_TYPES } from '@/components/ConfigurablePage/constants'

export default {
  name: 'FlexibleConfigExample',
  components: {
    ConfigurablePage
  },
  data() {
    return {
      pageConfig: {
        filter: {
          title: '灵活透传示例',
          labelWidth: '120px',
          items: [
            {
              prop: 'advancedInput',
              label: '高级输入框',
              type: COMPONENT_TYPES.INPUT,
              placeholder: '支持任意属性',
              width: '250px',

              // 任意Element UI Input属性都会自动透传
              clearable: true,
              showPassword: false,
              showWordLimit: true,
              maxlength: 50,
              minlength: 2,
              prefixIcon: 'el-icon-search',
              suffixIcon: 'el-icon-edit',
              size: 'medium',
              disabled: false,
              readonly: false,

              // 甚至可以设置一些不常用的属性
              autocomplete: 'off',
              tabindex: 1,

              // 任意事件都会自动透传
              events: {
                focus: (event) => {
                  console.log('🎯 输入框获得焦点:', event.target.value)
                },
                blur: (event) => {
                  console.log('👋 输入框失去焦点:', event.target.value)
                },
                clear: () => {
                  console.log('🧹 输入框被清空')
                },
                keydown: (event) => {
                  console.log('⌨️ 按键按下:', event.key)
                },
                keyup: (event) => {
                  console.log('⌨️ 按键抬起:', event.key)
                },
                mouseenter: () => {
                  console.log('🐭 鼠标进入输入框')
                },
                mouseleave: () => {
                  console.log('🐭 鼠标离开输入框')
                },
                // 甚至可以监听一些不常用的事件
                compositionstart: () => {
                  console.log('🈶 开始输入法输入')
                },
                compositionend: () => {
                  console.log('🈶 结束输入法输入')
                }
              }
            },
            {
              prop: 'flexibleSelect',
              label: '灵活下拉框',
              type: COMPONENT_TYPES.SELECT,
              placeholder: '支持任意Select属性',
              width: '200px',

              // 任意Select属性
              clearable: true,
              filterable: true,
              allowCreate: true,
              multiple: false,
              multipleLimit: 3,
              collapseTags: true,
              noMatchText: '无匹配数据',
              noDataText: '无数据',
              popperClass: 'custom-select-dropdown',
              reserveKeyword: true,
              defaultFirstOption: false,

              options: [
                { label: '选项1', value: 1 },
                { label: '选项2', value: 2 },
                { label: '选项3', value: 3 }
              ],

              events: {
                change: (value) => {
                  console.log('📝 选择改变:', value)
                },
                visibleChange: (visible) => {
                  console.log('👁️ 下拉框显示状态:', visible)
                },
                removeTag: (tag) => {
                  console.log('🏷️ 移除标签:', tag)
                },
                clear: () => {
                  console.log('🧹 清空选择')
                },
                focus: (event) => {
                  console.log('🎯 下拉框获得焦点')
                },
                blur: (event) => {
                  console.log('👋 下拉框失去焦点')
                }
              }
            },
            {
              prop: 'advancedDate',
              label: '高级日期选择',
              type: COMPONENT_TYPES.DATE,
              dateType: 'datetimerange',
              placeholder: '选择日期时间范围',
              width: '350px',

              // 任意DatePicker属性
              clearable: true,
              editable: true,
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              timeArrowControl: true,
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              unlinkPanels: true,

              events: {
                change: (value) => {
                  console.log('📅 日期改变:', value)
                },
                blur: () => {
                  console.log('👋 日期选择器失去焦点')
                },
                focus: () => {
                  console.log('🎯 日期选择器获得焦点')
                }
              }
            }
          ]
        },

        table: {
          title: '数据列表',
          columns: [
            { prop: 'id', label: 'ID', width: '80' },
            { prop: 'name', label: '名称', width: '150' },
            {
              prop: 'status',
              label: '状态',
              width: '100',
              type: COMPONENT_TYPES.TAG,
              tagType: (row) => {
                return row.status === 1 ? TAG_TYPES.SUCCESS : TAG_TYPES.DANGER
              },
              formatter: (row) => {
                return row.status === 1 ? '启用' : '禁用'
              }
            },
            {
              prop: 'actions',
              label: '操作',
              width: '200',
              fixed: 'right',
              type: COMPONENT_TYPES.ACTIONS,
              actions: [
                { name: 'edit', label: '编辑', type: BUTTON_TYPES.PRIMARY },
                { name: 'delete', label: '删除', type: BUTTON_TYPES.DANGER }
              ]
            }
          ]
        },

        dialog: {
          title: '编辑数据',
          width: '600px',
          labelWidth: '120px',
          items: [
            {
              prop: 'name',
              label: '名称',
              type: COMPONENT_TYPES.INPUT,
              placeholder: '请输入名称',
              required: true,

              // 弹窗表单中也支持完全透传
              clearable: true,
              maxlength: 30,
              showWordLimit: true,

              events: {
                input: (value) => {
                  console.log('📝 弹窗输入:', value)
                },
                change: (value) => {
                  console.log('📝 弹窗值改变:', value)
                }
              }
            }
          ]
        }
      }
    }
  },

  methods: {
    async fetchData(params) {
      console.log('🔍 获取数据，参数:', params)
      // 模拟数据
      return {
        data: [
          { id: 1, name: '测试数据1', status: 1 },
          { id: 2, name: '测试数据2', status: 0 }
        ],
        total: 2
      }
    },

    async submitData(data) {
      console.log('💾 提交数据:', data)
      return { success: true }
    },

    handleAction(action, row) {
      console.log('🎬 操作:', action, '行数据:', row)
    },

    handleDelete(row) {
      console.log('🗑️ 删除:', row)
    },

    handleFilterChange({ prop, value, item }) {
      console.log('🔄 筛选项改变:', { prop, value, item })
    },

    handleDialogChange({ prop, value, item }) {
      console.log('🔄 弹窗表单项改变:', { prop, value, item })
    }
  }
}
</script>

<style scoped>
.flexible-config-example {
  padding: 20px;
}

.flexible-config-example h2 {
  color: #409EFF;
  margin-bottom: 10px;
}

.flexible-config-example p {
  color: #666;
  margin-bottom: 20px;
}
</style>
