/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const configurablePageRouter = {
  path: '/configurable-page',
  component: Layout,
  redirect: '/configurable-page/basic',
  name: 'ConfigurablePage',
  meta: {
    title: '配置化页面',
    icon: 'component'
  },
  children: [
    {
      path: 'basic',
      component: () => import('@/views/example/ConfigurablePageExample'),
      name: 'BasicConfigurablePage',
      meta: { title: '基础示例' }
    },
    {
      path: 'advanced',
      component: () => import('@/views/example/AdvancedConfigurablePageExample'),
      name: 'AdvancedConfigurablePage',
      meta: { title: '高级示例' }
    }
  ]
}

export default configurablePageRouter
