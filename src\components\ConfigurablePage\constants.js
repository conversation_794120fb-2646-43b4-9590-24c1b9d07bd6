/**
 * ConfigurablePage 组件常量定义
 */

// 组件类型枚举
export const COMPONENT_TYPES = {
  // 表单组件类型
  INPUT: 'input',
  SELECT: 'select',
  DATE: 'date',
  TEXTAREA: 'textarea',
  SWITCH: 'switch',

  // 表格列类型
  TAG: 'tag',
  ACTIONS: 'actions',

  // 自定义渲染
  RENDER: 'render'
}

// 表单组件类型验证数组
export const FORM_COMPONENT_TYPES = [
  COMPONENT_TYPES.INPUT,
  COMPONENT_TYPES.SELECT,
  COMPONENT_TYPES.DATE,
  COMPONENT_TYPES.TEXTAREA,
  COMPONENT_TYPES.SWITCH
]

// 表格列类型验证数组
export const TABLE_COLUMN_TYPES = [
  COMPONENT_TYPES.TAG,
  COMPONENT_TYPES.ACTIONS
]

// 所有支持的组件类型
export const ALL_COMPONENT_TYPES = [
  ...FORM_COMPONENT_TYPES,
  ...TABLE_COLUMN_TYPES,
  COMPONENT_TYPES.RENDER
]

// 按钮类型枚举
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
  TEXT: 'text'
}

// 标签类型枚举
export const TAG_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info'
}

// 表单验证规则类型
export const VALIDATION_TYPES = {
  REQUIRED: 'required',
  EMAIL: 'email',
  URL: 'url',
  NUMBER: 'number',
  INTEGER: 'integer',
  FLOAT: 'float',
  PATTERN: 'pattern',
  MIN: 'min',
  MAX: 'max',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength'
}

// 默认配置
export const DEFAULT_CONFIG = {
  filter: {
    title: '筛选条件',
    showReset: true,
    items: []
  },
  table: {
    title: '数据列表',
    showAdd: true,
    selection: true,
    pagination: true,
    attrs: {
      border: true,
      stripe: true
    },
    columns: []
  },
  dialog: {
    title: '新增',
    editTitle: '编辑',
    width: '600px',
    items: []
  }
}

// 分页默认配置
export const DEFAULT_PAGINATION = {
  page: 1,
  size: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
}

// 常用事件名称枚举
export const EVENT_NAMES = {
  INPUT: 'input',
  CHANGE: 'change',
  FOCUS: 'focus',
  BLUR: 'blur',
  CLEAR: 'clear',
  CLICK: 'click',
  VISIBLE_CHANGE: 'visible-change',
  REMOVE_TAG: 'remove-tag'
}

// 常用属性名称枚举
export const PROP_NAMES = {
  DISABLED: 'disabled',
  READONLY: 'readonly',
  CLEARABLE: 'clearable',
  SIZE: 'size',
  MAXLENGTH: 'maxlength',
  SHOW_WORD_LIMIT: 'showWordLimit',
  MULTIPLE: 'multiple',
  FILTERABLE: 'filterable',
  ALLOW_CREATE: 'allowCreate',
  FORMAT: 'format',
  VALUE_FORMAT: 'valueFormat',
  RANGE_SEPARATOR: 'rangeSeparator'
}
