<template>
  <div class="configurable-page-example">
    <configurable-page
      :config="pageConfig"
      :fetch-data="fetchData"
      :submit-data="submitData"
      @action="handleAction"
      @delete="handleDelete"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script>
import ConfigurablePage from '@/components/ConfigurablePage'
import { COMPONENT_TYPES, BUTTON_TYPES, TAG_TYPES } from '@/components/ConfigurablePage/constants'

export default {
  name: 'ConfigurablePageExample',
  components: {
    ConfigurablePage
  },
  data() {
    return {
      pageConfig: {
        // 筛选表单配置
        filter: {
          title: '用户筛选',
          labelWidth: '100px',
          items: [
            {
              prop: 'name',
              label: '用户名',
              type: COMPONENT_TYPES.INPUT,
              placeholder: '请输入用户名',
              width: '200px',
              // 任意Element UI Input组件的属性都可以直接设置
              clearable: true,
              maxlength: 20,
              showWordLimit: true,
              prefixIcon: 'el-icon-user',
              suffixIcon: 'el-icon-search',
              // 任意事件都可以透传
              events: {
                focus: (event) => {
                  console.log('用户名输入框获得焦点:', event)
                },
                blur: (event) => {
                  console.log('用户名输入框失去焦点:', event)
                },
                clear: () => {
                  console.log('用户名输入框被清空')
                },
                keyup: (event) => {
                  console.log('按键抬起:', event.key)
                },
                // 甚至可以监听原生DOM事件
                mouseenter: () => {
                  console.log('鼠标进入输入框')
                }
              }
            },
            {
              prop: 'status',
              label: '状态',
              type: COMPONENT_TYPES.SELECT,
              placeholder: '请选择状态',
              width: '150px',
              // 任意Element UI Select组件的属性
              clearable: true,
              filterable: true,
              multiple: false,
              collapseTags: false,
              multipleLimit: 0,
              options: [
                { label: '全部', value: '' },
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ],
              // 任意事件都可以透传
              events: {
                change: (value) => {
                  console.log('状态选择改变:', value)
                },
                visibleChange: (visible) => {
                  console.log('下拉框显示状态:', visible)
                },
                removeTag: (tag) => {
                  console.log('移除标签:', tag)
                },
                clear: () => {
                  console.log('清空选择')
                }
              }
            },
            {
              prop: 'createTime',
              label: '创建时间',
              type: COMPONENT_TYPES.DATE,
              dateType: 'daterange',
              placeholder: '选择日期范围',
              width: '300px'
            },
            {
              prop: 'customField',
              label: '自定义字段',
              render(h, { value }) {
                return h('el-input', {
                  props: {
                    value: value,
                    placeholder: '自定义输入框'
                  },
                  on: {
                    input: (val) => {
                      this.$emit('input', val)
                    }
                  }
                })
              }
            }
            // 测试错误的箭头函数（注释掉以避免运行时错误）
            // {
            //   prop: 'testArrowFunction',
            //   label: '测试箭头函数',
            //   render: (h, { value }) => {
            //     // 这会抛出异常：renderFunc must be a regular function, not an arrow function
            //     return h('el-input', { props: { value } })
            //   }
            // }
          ]
        },

        // 表格配置
        table: {
          title: '用户列表',
          showAdd: true,
          selection: true,
          attrs: {
            border: true,
            stripe: true
          },
          columns: [
            {
              prop: 'id',
              label: 'ID',
              width: '80'
            },
            {
              prop: 'name',
              label: '用户名',
              minWidth: '120'
            },
            {
              prop: 'email',
              label: '邮箱',
              minWidth: '180'
            },
            {
              prop: 'status',
              label: '状态',
              width: '100',
              type: COMPONENT_TYPES.TAG,
              tagMap: [
                { value: 1, label: '启用', type: TAG_TYPES.SUCCESS },
                { value: 0, label: '禁用', type: TAG_TYPES.DANGER }
              ]
            },
            {
              prop: 'createTime',
              label: '创建时间',
              width: '180'
            },
            {
              prop: 'avatar',
              label: '头像',
              width: '100',
              render: (h, { row }) => {
                return h('el-avatar', {
                  props: {
                    size: 40,
                    src: row.avatar
                  }
                })
              }
            },
            {
              prop: 'actions',
              label: '操作',
              width: '200',
              fixed: 'right',
              type: COMPONENT_TYPES.ACTIONS,
              actions: [
                { name: 'edit', label: '编辑', type: BUTTON_TYPES.PRIMARY },
                { name: 'delete', label: '删除', type: BUTTON_TYPES.DANGER },
                { name: 'detail', label: '详情', type: BUTTON_TYPES.INFO }
              ]
            }
          ]
        },

        // 弹窗表单配置
        dialog: {
          width: '600px',
          addTitle: '新增用户',
          editTitle: '编辑用户',
          labelWidth: '100px',
          rules: {
            name: [
              { required: true, message: '请输入用户名', trigger: 'blur' }
            ],
            email: [
              { required: true, message: '请输入邮箱', trigger: 'blur' },
              { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
            ]
          },
          items: [
            {
              prop: 'name',
              label: '用户名',
              type: COMPONENT_TYPES.INPUT,
              placeholder: '请输入用户名',
              required: true
            },
            {
              prop: 'email',
              label: '邮箱',
              type: COMPONENT_TYPES.INPUT,
              placeholder: '请输入邮箱',
              required: true
            },
            {
              prop: 'phone',
              label: '手机号',
              type: COMPONENT_TYPES.INPUT,
              placeholder: '请输入手机号'
            },
            {
              prop: 'status',
              label: '状态',
              type: COMPONENT_TYPES.SELECT,
              placeholder: '请选择状态',
              defaultValue: 1,
              options: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ]
            },
            {
              prop: 'description',
              label: '描述',
              type: COMPONENT_TYPES.TEXTAREA,
              placeholder: '请输入描述',
              rows: 3
            },
            {
              prop: 'tags',
              label: '标签',
              render(h, { value }) {
                return h('el-select', {
                  props: {
                    value: value,
                    multiple: true,
                    placeholder: '请选择标签'
                  },
                  on: {
                    input: (val) => {
                      this.$emit('input', val)
                    }
                  }
                }, [
                  h('el-option', { props: { label: '前端', value: 'frontend' }}),
                  h('el-option', { props: { label: '后端', value: 'backend' }}),
                  h('el-option', { props: { label: '全栈', value: 'fullstack' }})
                ])
              }
            }
          ]
        }
      }
    }
  },
  methods: {
    // 模拟数据获取
    async fetchData(params) {
      console.log('获取数据参数:', params)

      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockData = {
            data: [
              {
                id: 1,
                name: '张三',
                email: '<EMAIL>',
                phone: '13800138001',
                status: 1,
                createTime: '2023-01-01 10:00:00',
                avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
                description: '前端开发工程师',
                tags: ['frontend']
              },
              {
                id: 2,
                name: '李四',
                email: '<EMAIL>',
                phone: '13800138002',
                status: 0,
                createTime: '2023-01-02 11:00:00',
                avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
                description: '后端开发工程师',
                tags: ['backend']
              }
            ],
            total: 2
          }
          resolve(mockData)
        }, 1000)
      })
    },

    // 模拟数据提交
    async submitData({ mode, data, originalData }) {
      console.log('提交数据:', { mode, data, originalData })

      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log(`${mode === 'add' ? '新增' : '编辑'}成功`)
          resolve(true)
        }, 1000)
      })
    },

    // 处理自定义操作
    handleAction({ action, row, index }) {
      console.log('自定义操作:', { action, row, index })

      if (action === 'detail') {
        this.$message.info(`查看用户详情: ${row.name}`)
      }
    },

    // 处理删除
    handleDelete({ row, index }) {
      console.log('删除用户:', { row, index })
      this.$message.success(`删除用户: ${row.name}`)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      console.log('选中的行:', selection)
    }
  }
}
</script>

<style lang="scss" scoped>
.configurable-page-example {
  padding: 20px;
}
</style>
